const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { SUCCESS } = require('../../constants/common').commonConstants;
const vendorService = require('./service');
const { createVendorSchema, updateVendorSchema, listVendorsSchema, uploadUrlSchema, validateId } = require('./validation');
const { messages } = require('../../messages');
const { getPresignedUrl, deleteFile } = require('../../utils/s3Service');

/**
 * Create a new vendor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const createVendor = async (req, res) => {
  try {
    const { error, value } = createVendorSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendor = await vendorService.createVendor(value, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.VENDOR_CREATED,
      status: true,
      data: vendor
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get vendor by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getVendorById = async (req, res) => {
  try {
    const { error } = validateId.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendor = await vendorService.getVendorById(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.VENDOR_RETRIEVED,
      status: true,
      data: vendor
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update vendor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const updateVendor = async (req, res) => {
  try {
    const { error: idError } = validateId.validate({ id: req.params.id });

    if (idError) {
      return res.status(400).json({
        message: idError.details[0].message,
        status: false
      });
    }

    const { error, value } = updateVendorSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendor = await vendorService.updateVendor(req.params.id, value, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.VENDOR_UPDATED,
      status: true,
      data: vendor
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete vendor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteVendor = async (req, res) => {
  try {
    const { error } = validateId.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    await vendorService.deleteVendor(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.VENDOR_DELETED,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * List vendors with pagination and filters
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const listVendors = async (req, res) => {
  try {
    const { error, value } = listVendorsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const result = await vendorService.listVendors(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.VENDORS_RETRIEVED,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get presigned URL for document upload
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { documentName } = req.params;

    if (![ 'gstCertificate', 'panCard', 'other' ].includes(documentName)) {
      return res.status(400).json({
        message: 'Invalid document name',
        status: false
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, `vendors/${documentName}`);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.UPLOAD_URL_GENERATED,
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete a document
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteDocument = async (req, res) => {
  try {
    const { key } = req.params;

    await deleteFile(key);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.VENDOR_DOCUMENT_DELETED,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createVendor,
  getVendorById,
  updateVendor,
  deleteVendor,
  listVendors,
  getUploadUrl,
  deleteDocument
};
