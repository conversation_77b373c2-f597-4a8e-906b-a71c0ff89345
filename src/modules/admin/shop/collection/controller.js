const { apiResponse, errorApiResponse } = require('../../../../config/responseHandler');
const { commonConstants } = require('../../../../constants/common');
const { messages } = require('../../../../messages');
const { getPresignedUrl } = require('../../../../utils/s3Service');
const collectionService = require('./service');
const { createCollectionSchema, updateCollectionSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Create a new collection
 */
const createCollection = async (req, res) => {
  try {
    const { error } = createCollectionSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    
    const data = await collectionService.createCollection({
      adminId,
      ...req.body
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.COLLECTION_CREATED || 'Collection created successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all collections
 */
const getAllCollections = async (req, res) => {
  try {
    const { includeInactive = false, limit = 10, page = 1, search, sortBy = 'createdAt', sortOrder = -1, category } = req.query;
    
    const data = await collectionService.getAllCollections(
      includeInactive === 'true', 
      parseInt(limit), 
      parseInt(page), 
      search, 
      sortBy, 
      parseInt(sortOrder),
      category
    );

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get collections by category ID
 */
const getCollectionsByCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { includeInactive = false } = req.query;
    
    const data = await collectionService.getCollectionsByCategory(categoryId, includeInactive === 'true');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get collection by ID
 */
const getCollectionById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await collectionService.getCollectionById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update collection
 */
const updateCollection = async (req, res) => {
  try {
    const { error } = updateCollectionSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    const { id } = req.params;
    
    const data = await collectionService.updateCollection({
      adminId,
      collectionId: id,
      ...req.body
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.COLLECTION_UPDATED || 'Collection updated successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete collection
 */
const deleteCollection = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await collectionService.deleteCollection(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.COLLECTION_DELETED || 'Collection deleted successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get upload URL for collection image
 */
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { extension } = req.body;
    
    const uploadData = await getPresignedUrl(extension, 'collections');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createCollection,
  getAllCollections,
  getCollectionsByCategory,
  getCollectionById,
  updateCollection,
  deleteCollection,
  getUploadUrl
};
