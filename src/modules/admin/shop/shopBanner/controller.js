const { apiResponse, errorApiResponse } = require('../../../../config/responseHandler');
const { commonConstants } = require('../../../../constants/common');
const { createBannerSchema, updateBannerSchema, uploadUrlSchema, validateId, deleteVideoSchema } = require('./validation');
const bannerService = require('./service');
const { getPresignedUrl } = require('../../../../utils/s3Service');
const { deleteFile } = require('../../../../utils/s3Service');
const { messages } = require('../../../../messages');

const createBanner = async (req, res) => {
  try {
    const { error } = createBannerSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await bannerService.createBanner(req.body, req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateBanner = async (req, res) => {
  try {
    const { error } = updateBannerSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const banner = await bannerService.updateBanner(
      req.params.id,
      req.body,
      req.user.id
    );

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: banner
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const listBanners = async (req, res) => {
  try {
    const data = await bannerService.listBanners(req.query);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteBanner = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const data = await bannerService.deleteBanner(req.params.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { extension } = req.body;

    const data = await getPresignedUrl(extension, 'shop_banner', req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getBannerById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const data = await bannerService.getBannerById(req.params.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const removeUploadedImage = async (req, res) => {
  try {

    const { error } = deleteVideoSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { key } = req.body;

    await deleteFile(key);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getUploadUrl,
  createBanner,
  updateBanner,
  listBanners,
  deleteBanner,
  getBannerById,
  removeUploadedImage
};
