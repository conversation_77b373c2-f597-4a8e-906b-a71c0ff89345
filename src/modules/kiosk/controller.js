const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const { messages } = require('../../messages');
const { getPresignedUrl, deleteFile } = require('../../utils/s3Service');
const kioskService = require('./service');
const { createKioskSchema, updateKioskSchema, validateId, listKiosksSchema, uploadUrlSchema, deletePoojaVideoSchema, updateKioskInventorySchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Create a new kiosk
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const createKiosk = async (req, res) => {
  try {
    const { error, value } = createKioskSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskService.createKiosk(value, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk created successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get a kiosk by ID with its associated users (read-only)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getKioskById = async (req, res) => {
  try {
    const { error } = validateId.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskService.getKioskById(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update a kiosk
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const updateKiosk = async (req, res) => {
  try {
    const idValidation = validateId.validate({ id: req.params.id });

    if (idValidation.error) {
      return res.status(400).json({
        status: false,
        message: idValidation.error.details[0].message
      });
    }

    const { error, value } = updateKioskSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskService.updateKiosk(req.params.id, value, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk updated successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete a kiosk and its associated users (users are automatically deleted)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteKiosk = async (req, res) => {
  try {
    const { error } = validateId.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskService.deleteKiosk(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk deleted successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * List kiosks with pagination and filtering
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const listKiosks = async (req, res) => {
  try {
    const { error, value } = listKiosksSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskService.listKiosks(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosks retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'virtual_pooja', req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deletePoojaVideo = async (req, res) => {
  try {

    const { error } = deletePoojaVideoSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const { key } = req.body;

    await deleteFile(key);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateKioskInventory = async (req, res) => {
  try {
    const { error } = updateKioskInventorySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const inventory = await kioskService.updateInventory(req.body, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.INVENTORY_RETRIEVED,
      data: inventory,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getKioskWithBelowThresholdInventory = async (req, res) => {
  try {
    const kiosks = await kioskService.getKioskWithBelowThresholdInventory();

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      data: kiosks,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createKiosk,
  getKioskById,
  updateKiosk,
  deleteKiosk,
  getUploadUrl,
  listKiosks,
  deletePoojaVideo,
  updateKioskInventory,
  getKioskWithBelowThresholdInventory
};
